<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SentryCoin v4.0 - Quantitative Analysis Dashboard</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            line-height: 1.6;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            padding: 1rem 2rem;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .header h1 {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .header .subtitle {
            opacity: 0.8;
            font-size: 1.1rem;
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1.5rem;
            padding: 2rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .card {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            border: 1px solid #333;
        }
        
        .card h3 {
            color: #4CAF50;
            margin-bottom: 1rem;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #333;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            color: #ccc;
        }
        
        .metric-value {
            font-weight: bold;
            font-size: 1.1rem;
        }
        
        .positive {
            color: #4CAF50;
        }
        
        .negative {
            color: #f44336;
        }
        
        .neutral {
            color: #ff9800;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }
        
        .status-active {
            background: #4CAF50;
            animation: pulse 2s infinite;
        }
        
        .status-warning {
            background: #ff9800;
        }
        
        .status-error {
            background: #f44336;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 1rem;
        }
        
        .alert-log {
            max-height: 300px;
            overflow-y: auto;
            background: #0f0f0f;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .alert-item {
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 6px;
            border-left: 4px solid;
        }
        
        .alert-predictive {
            background: #1a237e;
            border-left-color: #3f51b5;
        }
        
        .alert-confirmed {
            background: #1b5e20;
            border-left-color: #4caf50;
        }
        
        .alert-trifecta {
            background: #e65100;
            border-left-color: #ff9800;
        }
        
        .timestamp {
            font-size: 0.8rem;
            opacity: 0.7;
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }
        
        .comparison-table th,
        .comparison-table td {
            padding: 0.75rem;
            text-align: left;
            border-bottom: 1px solid #333;
        }
        
        .comparison-table th {
            background: #2a2a2a;
            color: #4CAF50;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ SentryCoin v4.0 - Quantitative Analysis Dashboard</h1>
        <div class="subtitle">Real-time Predictive Trading Analytics & Shadow P&L Monitoring</div>
    </div>

    <div class="dashboard">
        <!-- System Status -->
        <div class="card">
            <h3>🖥️ System Status</h3>
            <div class="metric">
                <span class="metric-label">Status</span>
                <span class="metric-value" id="system-status">
                    <span class="status-indicator status-active"></span>Active
                </span>
            </div>
            <div class="metric">
                <span class="metric-label">Uptime</span>
                <span class="metric-value" id="system-uptime">--</span>
            </div>
            <div class="metric">
                <span class="metric-label">Symbol</span>
                <span class="metric-value" id="system-symbol">SPKUSDT</span>
            </div>
            <div class="metric">
                <span class="metric-label">Version</span>
                <span class="metric-value">v4.0 Quantitative</span>
            </div>
        </div>

        <!-- Live Metrics -->
        <div class="card">
            <h3>📊 Live Metrics</h3>
            <div class="metric">
                <span class="metric-label">Order Flow Imbalance</span>
                <span class="metric-value" id="current-ofi">--</span>
            </div>
            <div class="metric">
                <span class="metric-label">Wavelet Energy</span>
                <span class="metric-value" id="wavelet-energy">--</span>
            </div>
            <div class="metric">
                <span class="metric-label">Shadow P&L</span>
                <span class="metric-value" id="shadow-pnl">$--</span>
            </div>
            <div class="metric">
                <span class="metric-label">Active Positions</span>
                <span class="metric-value" id="active-positions">--</span>
            </div>
        </div>

        <!-- Predictive Alerts -->
        <div class="card">
            <h3>🌊 Predictive Analysis</h3>
            <div class="metric">
                <span class="metric-label">Total Predictions</span>
                <span class="metric-value" id="total-predictions">--</span>
            </div>
            <div class="metric">
                <span class="metric-label">Confirmed</span>
                <span class="metric-value positive" id="confirmed-predictions">--</span>
            </div>
            <div class="metric">
                <span class="metric-label">Accuracy</span>
                <span class="metric-value" id="prediction-accuracy">--%</span>
            </div>
            <div class="metric">
                <span class="metric-label">Avg Lead Time</span>
                <span class="metric-value" id="avg-lead-time">--s</span>
            </div>
        </div>

        <!-- Strategy Comparison -->
        <div class="card">
            <h3>⚔️ Strategy Comparison</h3>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Strategy</th>
                        <th>Trades</th>
                        <th>P&L</th>
                        <th>Win Rate</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>🌊 Wavelet (Predictive)</td>
                        <td id="wavelet-trades">0</td>
                        <td id="wavelet-pnl" class="neutral">$0.00</td>
                        <td id="wavelet-winrate">0%</td>
                    </tr>
                    <tr>
                        <td>🎯 Trifecta (Reactive)</td>
                        <td id="trifecta-trades">0</td>
                        <td id="trifecta-pnl" class="neutral">$0.00</td>
                        <td id="trifecta-winrate">0%</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Wavelet Energy Chart -->
        <div class="card">
            <h3>📈 Wavelet Energy Time Series</h3>
            <div class="chart-container">
                <canvas id="energyChart"></canvas>
            </div>
        </div>

        <!-- Alert Log -->
        <div class="card">
            <h3>🚨 Recent Alerts</h3>
            <div class="alert-log" id="alert-log">
                <div class="alert-item alert-predictive">
                    <div>🌊 System initialized - Monitoring for predictive signals</div>
                    <div class="timestamp">Waiting for live data...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Chart setup
        const ctx = document.getElementById('energyChart').getContext('2d');
        const energyChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Wavelet Energy',
                    data: [],
                    borderColor: '#4CAF50',
                    backgroundColor: 'rgba(76, 175, 80, 0.1)',
                    tension: 0.4
                }, {
                    label: 'Threshold (3.5σ)',
                    data: [],
                    borderColor: '#f44336',
                    borderDash: [5, 5],
                    fill: false
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: '#ffffff' }
                    }
                },
                scales: {
                    x: { 
                        ticks: { color: '#ffffff' },
                        grid: { color: '#333' }
                    },
                    y: { 
                        ticks: { color: '#ffffff' },
                        grid: { color: '#333' }
                    }
                }
            }
        });

        // Socket event handlers
        socket.on('connect', () => {
            console.log('Connected to dashboard');
            addAlert('🔗 Connected to SentryCoin v4.0 Dashboard', 'system');
        });

        socket.on('dashboard-data', (data) => {
            updateDashboard(data);
        });

        socket.on('predictive-alert', (alert) => {
            addAlert(`🌊 PREDICTIVE CASCADE ALERT - Z-score: ${alert.zScore.toFixed(2)}σ`, 'predictive');
            updatePredictiveMetrics();
        });

        socket.on('prediction-confirmed', (alert) => {
            addAlert(`✅ Prediction confirmed - Lead time: ${alert.actualLeadTime.toFixed(1)}s`, 'confirmed');
        });

        socket.on('trifecta-signal', (data) => {
            addAlert(`🎯 TRIFECTA CONVICTION SIGNAL - ${data.confirmedAlerts.length} predictions confirmed`, 'trifecta');
        });

        socket.on('strategy-update', (comparison) => {
            updateStrategyComparison(comparison);
        });

        // Update functions
        function updateDashboard(data) {
            if (data.overview && data.overview.system) {
                const system = data.overview.system;
                document.getElementById('system-uptime').textContent = formatUptime(system.uptime);
                document.getElementById('system-symbol').textContent = system.symbol;
            }

            if (data.liveMetrics) {
                document.getElementById('current-ofi').textContent = data.liveMetrics.ofi.toFixed(4);
                document.getElementById('wavelet-energy').textContent = data.liveMetrics.waveletEnergy.toFixed(2);
                document.getElementById('shadow-pnl').textContent = `$${data.liveMetrics.shadowPnL.toFixed(2)}`;
            }
        }

        function updatePredictiveMetrics() {
            fetch('/api/dashboard/predictive-alerts')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('total-predictions').textContent = data.alerts.length;
                    document.getElementById('confirmed-predictions').textContent = data.summary.confirmed;
                    const accuracy = data.summary.confirmed / data.alerts.length * 100;
                    document.getElementById('prediction-accuracy').textContent = `${accuracy.toFixed(1)}%`;
                    document.getElementById('avg-lead-time').textContent = `${data.summary.averageLeadTime.toFixed(1)}s`;
                });
        }

        function updateStrategyComparison(comparison) {
            // Wavelet strategy
            document.getElementById('wavelet-trades').textContent = comparison.wavelet.trades;
            document.getElementById('wavelet-pnl').textContent = `$${comparison.wavelet.pnl.toFixed(2)}`;
            document.getElementById('wavelet-winrate').textContent = `${comparison.wavelet.winRate.toFixed(1)}%`;
            
            // Trifecta strategy
            document.getElementById('trifecta-trades').textContent = comparison.trifecta.trades;
            document.getElementById('trifecta-pnl').textContent = `$${comparison.trifecta.pnl.toFixed(2)}`;
            document.getElementById('trifecta-winrate').textContent = `${comparison.trifecta.winRate.toFixed(1)}%`;
            
            // Update colors based on P&L
            updatePnLColor('wavelet-pnl', comparison.wavelet.pnl);
            updatePnLColor('trifecta-pnl', comparison.trifecta.pnl);
        }

        function updatePnLColor(elementId, pnl) {
            const element = document.getElementById(elementId);
            element.className = pnl > 0 ? 'positive' : pnl < 0 ? 'negative' : 'neutral';
        }

        function addAlert(message, type) {
            const alertLog = document.getElementById('alert-log');
            const alertItem = document.createElement('div');
            alertItem.className = `alert-item alert-${type}`;
            alertItem.innerHTML = `
                <div>${message}</div>
                <div class="timestamp">${new Date().toLocaleTimeString()}</div>
            `;
            alertLog.insertBefore(alertItem, alertLog.firstChild);
            
            // Keep only last 20 alerts
            while (alertLog.children.length > 20) {
                alertLog.removeChild(alertLog.lastChild);
            }
        }

        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }

        // Periodic updates
        setInterval(() => {
            fetch('/api/dashboard/live-metrics')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('current-ofi').textContent = data.ofi.toFixed(4);
                    document.getElementById('wavelet-energy').textContent = data.waveletEnergy.toFixed(2);
                    document.getElementById('shadow-pnl').textContent = `$${data.shadowTradingPnL.toFixed(2)}`;
                    document.getElementById('active-positions').textContent = data.activePositions;
                });
        }, 5000);

        // Update energy chart
        setInterval(() => {
            fetch('/api/dashboard/wavelet-energy?count=60')
                .then(response => response.json())
                .then(data => {
                    const labels = data.energyScores.map(e => new Date(e.timestamp).toLocaleTimeString());
                    const energyData = data.energyScores.map(e => e.energy);
                    const thresholdData = new Array(energyData.length).fill(data.statistics.threshold);
                    
                    energyChart.data.labels = labels;
                    energyChart.data.datasets[0].data = energyData;
                    energyChart.data.datasets[1].data = thresholdData;
                    energyChart.update('none');
                });
        }, 10000);
    </script>
</body>
</html>
